<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class WhatsAppProfile extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'profiles';

    protected $fillable = [
        'whatsapp_id',
        'phone_number',
        'name',
        'profile_picture',
        'is_business',
        'is_group',
        'metadata',
        'last_seen',
    ];

    protected $casts = [
        'is_business' => 'boolean',
        'is_group' => 'boolean',
        'metadata' => 'array',
        'last_seen' => 'datetime',
    ];

    /**
     * Get all messages for this profile
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'profile_id');
    }

    /**
     * Get the conversation for this profile
     */
    public function conversation(): HasOne
    {
        return $this->hasOne(WhatsAppConversation::class, 'profile_id');
    }

    /**
     * Get the latest message
     */
    public function latestMessage()
    {
        return $this->messages()->latest('sent_at')->first();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'instagram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Instagram Settings Table
        Schema::connection('instagram')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('username')->nullable();
            $table->string('access_token')->nullable();
            $table->string('user_id_instagram')->nullable(); // Instagram user ID
            $table->boolean('auto_post')->default(false);
            $table->boolean('auto_like')->default(false);
            $table->boolean('auto_comment')->default(false);
            $table->boolean('auto_follow')->default(false);
            $table->boolean('story_posting')->default(false);
            $table->integer('max_posts_per_day')->default(5);
            $table->json('hashtags')->nullable(); // Default hashtags
            $table->timestamps();
        });

        // Instagram Profiles Table (Followers/Following)
        Schema::connection('instagram')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('instagram_id')->unique(); // Instagram user ID
            $table->string('username');
            $table->string('full_name')->nullable();
            $table->string('profile_picture')->nullable();
            $table->text('bio')->nullable();
            $table->integer('followers_count')->default(0);
            $table->integer('following_count')->default(0);
            $table->integer('posts_count')->default(0);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_business')->default(false);
            $table->boolean('is_following')->default(false);
            $table->boolean('follows_me')->default(false);
            $table->json('metadata')->nullable(); // Additional profile info
            $table->timestamps();
        });

        // Instagram Messages Table (DMs)
        Schema::connection('instagram')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Instagram message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'image', 'video', 'audio', 'story_reply', 'post_share']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For media messages
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->boolean('is_read')->default(false);
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // Instagram Posts Table
        Schema::connection('instagram')->create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('post_id')->unique(); // Instagram post ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['photo', 'video', 'carousel', 'story']);
            $table->text('caption')->nullable();
            $table->string('media_url');
            $table->integer('likes_count')->default(0);
            $table->integer('comments_count')->default(0);
            $table->json('hashtags')->nullable();
            $table->timestamp('posted_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('instagram')->dropIfExists('posts');
        Schema::connection('instagram')->dropIfExists('messages');
        Schema::connection('instagram')->dropIfExists('profiles');
        Schema::connection('instagram')->dropIfExists('settings');
    }
};

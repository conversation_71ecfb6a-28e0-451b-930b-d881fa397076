<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\WhatsApp\WhatsAppSettings;
use App\Models\WhatsApp\WhatsAppProfile;
use App\Models\WhatsApp\WhatsAppMessage;
use App\Models\WhatsApp\WhatsAppConversation;

class WhatsAppController extends Controller
{
    private $whatsappServerUrl;

    public function __construct()
    {
        $this->whatsappServerUrl = config('app.whatsapp_server_url', 'http://localhost:3001');
    }

    /**
     * Handle webhook from WhatsApp Web server
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $event = $request->input('event');
            $data = $request->input('data');
            
            Log::info('WhatsApp webhook received', ['event' => $event, 'data' => $data]);
            
            switch ($event) {
                case 'qr_generated':
                    $this->handleQRGenerated($data);
                    break;
                    
                case 'connected':
                    $this->handleConnected($data);
                    break;
                    
                case 'authenticated':
                    $this->handleAuthenticated($data);
                    break;
                    
                case 'auth_failure':
                    $this->handleAuthFailure($data);
                    break;
                    
                case 'disconnected':
                    $this->handleDisconnected($data);
                    break;
                    
                case 'message_received':
                    $this->handleMessageReceived($data);
                    break;
                    
                case 'store_message':
                    $this->storeMessage($data);
                    break;
                    
                default:
                    Log::warning('Unknown WhatsApp webhook event', ['event' => $event]);
            }
            
            return response()->json(['success' => true]);
            
        } catch (\Exception $e) {
            Log::error('WhatsApp webhook error', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get WhatsApp status from Node.js server
     */
    public function getStatus(): JsonResponse
    {
        try {
            $response = Http::timeout(10)->get($this->whatsappServerUrl . '/api/status');
            
            if ($response->successful()) {
                return response()->json($response->json());
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get WhatsApp status'
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'WhatsApp server is not running'
            ], 500);
        }
    }

    /**
     * Generate QR code
     */
    public function generateQR(): JsonResponse
    {
        try {
            $response = Http::timeout(10)->post($this->whatsappServerUrl . '/api/generate-qr');
            
            if ($response->successful()) {
                return response()->json($response->json());
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code'
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'WhatsApp server is not running'
            ], 500);
        }
    }

    /**
     * Disconnect WhatsApp
     */
    public function disconnect(): JsonResponse
    {
        try {
            $response = Http::timeout(10)->post($this->whatsappServerUrl . '/api/disconnect');
            
            if ($response->successful()) {
                // Update database
                $this->updateAllSettingsDisconnected();
                
                return response()->json($response->json());
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to disconnect WhatsApp'
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'WhatsApp server is not running'
            ], 500);
        }
    }

    /**
     * Send message via WhatsApp
     */
    public function sendMessage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'to' => 'required|string',
                'message' => 'required|string'
            ]);
            
            $response = Http::timeout(30)->post($this->whatsappServerUrl . '/api/send-message', [
                'to' => $request->input('to'),
                'message' => $request->input('message')
            ]);
            
            if ($response->successful()) {
                return response()->json($response->json());
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message'
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle QR code generated event
     */
    private function handleQRGenerated($data)
    {
        // Update settings with QR code
        $settings = WhatsAppSettings::where('user_id', 1)->first();
        if ($settings) {
            $settings->update([
                'qr_code' => $data['qr'],
                'connected' => false
            ]);
        }
    }

    /**
     * Handle connected event
     */
    private function handleConnected($data)
    {
        $clientInfo = $data['clientInfo'];
        
        // Update settings
        $settings = WhatsAppSettings::where('user_id', 1)->first();
        if ($settings) {
            $settings->update([
                'connected' => true,
                'qr_code' => null,
                'client_info' => $clientInfo,
                'session_id' => $clientInfo['id']
            ]);
        }
    }

    /**
     * Handle authenticated event
     */
    private function handleAuthenticated($data)
    {
        Log::info('WhatsApp authenticated');
    }

    /**
     * Handle auth failure event
     */
    private function handleAuthFailure($data)
    {
        $this->updateAllSettingsDisconnected();
        Log::error('WhatsApp authentication failed', $data);
    }

    /**
     * Handle disconnected event
     */
    private function handleDisconnected($data)
    {
        $this->updateAllSettingsDisconnected();
        Log::info('WhatsApp disconnected', $data);
    }

    /**
     * Handle message received event
     */
    private function handleMessageReceived($data)
    {
        // This will trigger the store_message event
        Log::info('WhatsApp message received', $data);
    }

    /**
     * Store message in database
     */
    private function storeMessage($data)
    {
        try {
            // Create or update profile
            $profile = WhatsAppProfile::updateOrCreate(
                ['whatsapp_id' => $data['contact']['id']],
                [
                    'phone_number' => $data['from'],
                    'name' => $data['contact']['name'] ?? $data['contact']['pushname'],
                    'profile_picture' => $data['contact']['profilePicUrl'],
                    'is_group' => $data['isGroupMsg'] ?? false,
                    'metadata' => $data['contact']
                ]
            );

            // Store message
            $message = WhatsAppMessage::create([
                'message_id' => $data['message_id'],
                'profile_id' => $profile->id,
                'type' => $data['type'],
                'content' => $data['body'],
                'is_outgoing' => false,
                'is_read' => false,
                'sent_at' => now()
            ]);

            // Update or create conversation
            $conversation = WhatsAppConversation::updateOrCreate(
                ['profile_id' => $profile->id],
                [
                    'last_message_id' => $message->id,
                    'last_activity' => now()
                ]
            );

            // Increment unread count
            $conversation->incrementUnread();

            Log::info('WhatsApp message stored', ['message_id' => $message->id]);

        } catch (\Exception $e) {
            Log::error('Error storing WhatsApp message', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Update all settings to disconnected state
     */
    private function updateAllSettingsDisconnected()
    {
        WhatsAppSettings::query()->update([
            'connected' => false,
            'qr_code' => null,
            'client_info' => null,
            'session_id' => null
        ]);
    }
}

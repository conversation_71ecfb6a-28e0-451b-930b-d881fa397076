<div class="space-y-8">

    @if(!$isConnected)
        <!-- QR Code Section -->
        <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-2xl shadow-lg overflow-hidden">
            @if($sessionStatus === 'connecting' && $qrCode)
                <!-- Timer Header -->
                <div class="bg-amber-50 dark:bg-amber-900/20 border-b border-amber-200 dark:border-amber-800 p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-amber-600 dark:text-amber-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-sm font-semibold text-amber-800 dark:text-amber-200">QR Code Expires In</h3>
                                <p class="text-xs text-amber-600 dark:text-amber-400">Automatically refreshes when expired</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span id="qr-timer" class="text-2xl font-bold text-amber-700 dark:text-amber-300 tabular-nums">
                                <span id="minutes">2</span>:<span id="seconds">00</span>
                            </span>
                            <button wire:click="forceRefreshQr"
                                class="text-xs px-3 py-1.5 bg-amber-200 dark:bg-amber-700 text-amber-800 dark:text-amber-200 rounded hover:bg-amber-300 dark:hover:bg-amber-600 transition-colors">
                                <i class="fas fa-redo mr-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <!-- QR Code Section -->
                        <div class="flex-1 lg:max-w-sm">
                            <div class="bg-white dark:bg-navy-800 rounded-lg p-4 border border-slate-200 dark:border-navy-600">
                                <img src="{{ $qrCode }}" alt="WhatsApp QR Code" class="w-full max-w-80 lg:max-w-64 mx-auto rounded-lg" />
                            </div>
                        </div>

                        <!-- Instructions Section -->
                        <div class="flex-1 space-y-4">
                                <h4 class="text-sm font-semibold text-slate-800 dark:text-navy-100 flex items-center">
                                    <i class="fab fa-whatsapp mr-2 text-green-500"></i>
                                    Scan with WhatsApp
                                </h4>

                                <div class="space-y-2 text-xs text-slate-600 dark:text-navy-300">
                                    <div class="flex items-start space-x-2 p-2 lg:p-3 bg-slate-50 dark:bg-navy-600 rounded-lg">
                                        <span class="w-4 h-4 lg:w-5 lg:h-5 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">1</span>
                                        <div>
                                            <p class="font-medium text-slate-800 dark:text-navy-100 text-xs lg:text-sm">Open WhatsApp</p>
                                            <p class="text-slate-600 dark:text-navy-300 text-xs">Settings → Linked Devices</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-2 p-2 lg:p-3 bg-slate-50 dark:bg-navy-600 rounded-lg">
                                        <span class="w-4 h-4 lg:w-5 lg:h-5 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">2</span>
                                        <div>
                                            <p class="font-medium text-slate-800 dark:text-navy-100 text-xs lg:text-sm">Scan QR Code</p>
                                            <p class="text-slate-600 dark:text-navy-300 text-xs">Tap "Link a Device" and scan</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="flex items-center space-x-2 text-xs bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                    <span class="text-blue-700 dark:text-blue-300 font-medium">Waiting for scan...</span>
                                </div>

                                <!-- Buttons -->
                                <div class="flex flex-col sm:flex-row gap-2">
                                    <button wire:click="refreshQrCode"
                                        wire:loading.attr="disabled"
                                        wire:target="refreshQrCode"
                                        class="btn border border-slate-300 text-slate-700 hover:bg-slate-50 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-600 rounded px-3 py-2 text-xs transition-colors disabled:opacity-50 flex-1 sm:flex-none">
                                        <span wire:loading.remove wire:target="refreshQrCode">
                                            <i class="fas fa-sync-alt mr-1"></i>Refresh Code
                                        </span>
                                        <span wire:loading wire:target="refreshQrCode">
                                            <i class="fas fa-spinner fa-spin mr-1"></i>Loading...
                                        </span>
                                    </button>

                                    <button wire:click="simulateConnection"
                                        class="btn bg-green-500 text-white hover:bg-green-600 rounded px-3 py-2 text-xs transition-colors flex-1 sm:flex-none">
                                        <i class="fas fa-check mr-1"></i>Test Connection
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="p-6">
                        <div class="text-center space-y-4">
                            <!-- WhatsApp Icon -->
                            <div class="relative inline-block">
                                <div class="w-24 h-24 mx-auto bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-2xl flex items-center justify-center shadow-lg">
                                    <i class="fab fa-whatsapp text-4xl text-green-500"></i>
                                </div>
                                <div class="absolute -bottom-1 -right-1 size-6 bg-slate-200 dark:bg-navy-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-qrcode text-slate-500 dark:text-navy-300 text-sm"></i>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="space-y-2 max-w-sm mx-auto">
                                <h5 class="text-base font-medium text-slate-800 dark:text-navy-100">Ready to Connect</h5>
                                <p class="text-sm text-slate-600 dark:text-navy-300">
                                    Generate a QR code to securely link your WhatsApp account
                                </p>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-center space-x-3">
                                <button wire:click="generateQrCode"
                                    wire:loading.attr="disabled"
                                    wire:target="generateQrCode"
                                    class="btn bg-gradient-to-r from-green-500 to-emerald-600 font-medium text-white hover:from-green-600 hover:to-emerald-700 focus:from-green-600 focus:to-emerald-700 active:from-green-700 active:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg px-6 py-3 text-sm shadow-lg transition-all duration-200">
                                    <span wire:loading.remove wire:target="generateQrCode">
                                        <i class="fas fa-qrcode mr-2"></i>
                                        Generate QR Code
                                    </span>
                                    <span wire:loading wire:target="generateQrCode">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Generating...
                                    </span>
                                </button>

                                <button wire:click="refreshStatus"
                                    wire:loading.attr="disabled"
                                    wire:target="refreshStatus"
                                    class="btn border border-slate-300 dark:border-navy-450 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg px-4 py-3 text-sm transition-all duration-200">
                                    <span wire:loading.remove wire:target="refreshStatus">
                                        <i class="fas fa-sync-alt mr-2"></i>
                                        Refresh
                                    </span>
                                    <span wire:loading wire:target="refreshStatus">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Refreshing...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @else
        <!-- Connected User Information -->
        <div class="space-y-6">
            <div class="text-center">
                <h4 class="text-base font-medium text-slate-800 dark:text-navy-100 mb-2">Connected Account</h4>
                <p class="text-sm text-slate-600 dark:text-navy-300">Your WhatsApp account is successfully linked</p>
            </div>

            <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-2xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center space-x-4">
                        <!-- Profile Picture -->
                        <div class="relative">
                            <div class="avatar size-16 ring-2 ring-green-100 dark:ring-green-900/50">
                                @if(isset($userInfo['profilePicUrl']) && $userInfo['profilePicUrl'])
                                    <img src="{{ $userInfo['profilePicUrl'] }}" alt="Profile Picture" class="rounded-full shadow-lg" />
                                @else
                                    <div class="is-initial rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white text-lg shadow-lg">
                                        <i class="fas fa-user"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="absolute -bottom-1 -right-1 size-5 rounded-full bg-green-500 border-2 border-white dark:border-navy-700 flex items-center justify-center shadow-lg">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>

                        <!-- User Details -->
                        <div class="flex-1">
                            <div class="space-y-1">
                                <h5 class="text-base font-medium text-slate-800 dark:text-navy-100">
                                    {{ $userInfo['pushname'] ?? $userInfo['name'] ?? 'WhatsApp User' }}
                                </h5>
                                <p class="text-sm text-slate-600 dark:text-navy-300 font-medium">
                                    {{ $userInfo['id'] ?? 'Unknown Number' }}
                                </p>
                                @if($connectionTime)
                                    <div class="flex items-center space-x-1 text-xs text-slate-500 dark:text-navy-400">
                                        <i class="fas fa-clock"></i>
                                        <span>Connected {{ \Carbon\Carbon::parse($connectionTime)->diffForHumans() }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Status Badge -->
                        <div class="text-right">
                            <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded-full text-sm font-medium shadow-sm">
                                <div class="size-2 rounded-full bg-green-500 mr-1 animate-pulse"></div>
                                Active
                            </div>
                        </div>
                    </div>

                    @if(isset($userInfo['platform']))
                        <div class="mt-4 pt-4 border-t border-slate-200 dark:border-navy-500">
                            <div class="flex items-center justify-center space-x-6">
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center size-10 bg-slate-100 dark:bg-navy-600 rounded-lg mb-1">
                                        <i class="fas fa-mobile-alt text-slate-600 dark:text-navy-300"></i>
                                    </div>
                                    <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Platform</p>
                                    <p class="text-sm font-medium text-slate-700 dark:text-navy-200">{{ ucfirst($userInfo['platform']) }}</p>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center size-10 bg-green-100 dark:bg-green-900/30 rounded-lg mb-1">
                                        <i class="fab fa-whatsapp text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Status</p>
                                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Online</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Action Buttons -->
    @if($isConnected)
        <div class="flex justify-center pt-4">
            <button wire:click="disconnect"
                wire:loading.attr="disabled"
                wire:target="disconnect"
                class="btn border border-red-300 text-red-600 hover:bg-red-50 focus:bg-red-50 active:bg-red-100 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:focus:bg-red-900/20 dark:active:bg-red-900/30 rounded-lg px-6 py-2 text-sm font-medium shadow-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <span wire:loading.remove wire:target="disconnect">
                    <i class="fas fa-unlink mr-1"></i>
                    Disconnect WhatsApp
                </span>
                <span wire:loading wire:target="disconnect">
                    <i class="fas fa-spinner fa-spin mr-1"></i>
                    Disconnecting...
                </span>
            </button>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let pollingInterval = null;
    let qrTimerInterval = null;
    let qrTimeRemaining = 120; // 2 minutes in seconds

    // Start polling when connecting
    function startPolling() {
        if (pollingInterval) return;

        pollingInterval = setInterval(() => {
            @this.call('checkConnectionStatus');
        }, 3000); // Poll every 3 seconds
    }

    // Stop polling
    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
    }

    // Start QR timer countdown (CLIENT-SIDE)
    function startQrTimer() {
        if (qrTimerInterval) return;

        // Reset timer to 2 minutes
        qrTimeRemaining = 120;
        updateTimerDisplay();

        qrTimerInterval = setInterval(() => {
            qrTimeRemaining--;
            updateTimerDisplay();

            // If timer expires, call server to refresh QR
            if (qrTimeRemaining <= 0) {
                stopQrTimer();
                @this.call('qrExpired');
            }
        }, 1000); // Update every second
    }

    // Update timer display
    function updateTimerDisplay() {
        const minutes = Math.floor(qrTimeRemaining / 60);
        const seconds = qrTimeRemaining % 60;

        const minutesEl = document.getElementById('minutes');
        const secondsEl = document.getElementById('seconds');

        if (minutesEl && secondsEl) {
            minutesEl.textContent = minutes;
            secondsEl.textContent = seconds.toString().padStart(2, '0');
        }
    }

    // Stop QR timer
    function stopQrTimer() {
        if (qrTimerInterval) {
            clearInterval(qrTimerInterval);
            qrTimerInterval = null;
        }
    }

    // Listen for Livewire events
    Livewire.on('start-polling', () => {
        startPolling();
    });

    Livewire.on('stop-polling', () => {
        stopPolling();
        stopQrTimer();
    });

    Livewire.on('start-qr-timer', () => {
        startQrTimer();
    });

    // Auto-start polling and timer based on session status
    setTimeout(() => {
        const sessionStatus = @json($sessionStatus);

        if (sessionStatus === 'connecting') {
            startPolling();
            startQrTimer();
        } else if (sessionStatus === 'disconnected') {
            startPolling();
        } else if (sessionStatus === 'connected') {
            stopPolling();
            stopQrTimer();
        }
    }, 1000); // Wait 1 second for component to fully initialize

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
        stopPolling();
        stopQrTimer();
    });
});
</script>

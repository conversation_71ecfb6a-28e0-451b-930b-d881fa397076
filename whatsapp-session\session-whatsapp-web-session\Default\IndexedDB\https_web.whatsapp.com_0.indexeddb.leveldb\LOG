2025/06/16-22:21:34.160 7b9c Creating DB D:\Laravel_Projects\alhars\whatsapp-session\session-whatsapp-web-session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/06/16-22:21:34.173 7b9c Reusing MANIFEST D:\Laravel_Projects\alhars\whatsapp-session\session-whatsapp-web-session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/16-22:21:34.584 8a60 Level-0 table #5: started
2025/06/16-22:21:34.593 8a60 Level-0 table #5: 11138 bytes OK
2025/06/16-22:21:34.601 8a60 Delete type=0 #3
2025/06/16-22:21:34.601 8a60 Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.605 8098 Level-0 table #7: started
2025/06/16-22:21:34.618 8098 Level-0 table #7: 863 bytes OK
2025/06/16-22:21:34.623 8098 Delete type=0 #4
2025/06/16-22:21:34.624 8098 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.624 8098 Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 754 : 0
2025/06/16-22:21:34.624 8098 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.628 8098 Generated table #8@1: 227 keys, 4800 bytes
2025/06/16-22:21:34.628 8098 Compacted 1@1 + 1@2 files => 4800 bytes
2025/06/16-22:21:34.635 8098 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.636 8098 Delete type=2 #7
2025/06/16-22:21:34.636 8098 Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 754 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.640 a64 Level-0 table #10: started
2025/06/16-22:21:34.661 a64 Level-0 table #10: 471 bytes OK
2025/06/16-22:21:34.666 a64 Delete type=2 #5
2025/06/16-22:21:34.667 a64 Delete type=0 #6
2025/06/16-22:21:34.667 a64 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.667 a64 Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x05\x00\x00\x05' @ 768 : 0
2025/06/16-22:21:34.667 a64 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.682 a64 Generated table #11@1: 224 keys, 4776 bytes
2025/06/16-22:21:34.682 a64 Compacted 1@1 + 1@2 files => 4776 bytes
2025/06/16-22:21:34.696 a64 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.696 a64 Delete type=2 #10
2025/06/16-22:21:34.696 a64 Manual compaction at level-1 from '\x00\x05\x00\x00\x05' @ 768 : 0 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.697 a64 Level-0 table #13: started
2025/06/16-22:21:34.711 a64 Level-0 table #13: 2223 bytes OK
2025/06/16-22:21:34.717 a64 Delete type=2 #8
2025/06/16-22:21:34.717 a64 Delete type=0 #9
2025/06/16-22:21:34.717 a64 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.717 a64 Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 874 : 1
2025/06/16-22:21:34.717 a64 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.723 a64 Generated table #14@1: 291 keys, 6213 bytes
2025/06/16-22:21:34.723 a64 Compacted 1@1 + 1@2 files => 6213 bytes
2025/06/16-22:21:34.731 a64 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.731 a64 Delete type=2 #13
2025/06/16-22:21:34.731 a64 Manual compaction at level-1 from '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 874 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.732 a64 Level-0 table #16: started
2025/06/16-22:21:34.746 a64 Level-0 table #16: 2877 bytes OK
2025/06/16-22:21:34.751 a64 Delete type=2 #11
2025/06/16-22:21:34.751 a64 Delete type=0 #12
2025/06/16-22:21:34.751 a64 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.751 a64 Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1054 : 1
2025/06/16-22:21:34.751 a64 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.758 a64 Generated table #17@1: 448 keys, 8514 bytes
2025/06/16-22:21:34.758 a64 Compacted 1@1 + 1@2 files => 8514 bytes
2025/06/16-22:21:34.762 a64 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.763 a64 Delete type=2 #16
2025/06/16-22:21:34.763 a64 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1054 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.764 4468 Level-0 table #19: started
2025/06/16-22:21:34.778 4468 Level-0 table #19: 249 bytes OK
2025/06/16-22:21:34.783 4468 Delete type=2 #14
2025/06/16-22:21:34.783 4468 Delete type=0 #15
2025/06/16-22:21:34.784 4468 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.784 76d0 Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 1074 : 0
2025/06/16-22:21:34.784 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.789 76d0 Generated table #20@1: 442 keys, 8468 bytes
2025/06/16-22:21:34.789 76d0 Compacted 1@1 + 1@2 files => 8468 bytes
2025/06/16-22:21:34.794 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.794 76d0 Delete type=2 #19
2025/06/16-22:21:34.794 76d0 Manual compaction at level-1 from '\x00\x08\x00\x00\x05' @ 1074 : 0 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.795 76d0 Level-0 table #22: started
2025/06/16-22:21:34.812 76d0 Level-0 table #22: 249 bytes OK
2025/06/16-22:21:34.815 76d0 Delete type=2 #17
2025/06/16-22:21:34.815 76d0 Delete type=0 #18
2025/06/16-22:21:34.815 76d0 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.815 76d0 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 1080 : 0
2025/06/16-22:21:34.815 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.822 76d0 Generated table #23@1: 436 keys, 8391 bytes
2025/06/16-22:21:34.822 76d0 Compacted 1@1 + 1@2 files => 8391 bytes
2025/06/16-22:21:34.826 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.827 76d0 Delete type=2 #22
2025/06/16-22:21:34.827 76d0 Manual compaction at level-1 from '\x00\x09\x00\x00\x05' @ 1080 : 0 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.827 76d0 Level-0 table #25: started
2025/06/16-22:21:34.847 76d0 Level-0 table #25: 249 bytes OK
2025/06/16-22:21:34.849 76d0 Delete type=2 #20
2025/06/16-22:21:34.849 76d0 Delete type=0 #21
2025/06/16-22:21:34.850 76d0 Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.850 76d0 Manual compaction at level-1 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 1086 : 0
2025/06/16-22:21:34.850 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.856 76d0 Generated table #26@1: 430 keys, 8146 bytes
2025/06/16-22:21:34.856 76d0 Compacted 1@1 + 1@2 files => 8146 bytes
2025/06/16-22:21:34.859 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.860 76d0 Delete type=2 #25
2025/06/16-22:21:34.860 76d0 Manual compaction at level-1 from '\x00\x0d\x00\x00\x05' @ 1086 : 0 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.860 76d0 Level-0 table #28: started
2025/06/16-22:21:34.877 76d0 Level-0 table #28: 249 bytes OK
2025/06/16-22:21:34.880 76d0 Delete type=2 #23
2025/06/16-22:21:34.880 76d0 Delete type=0 #24
2025/06/16-22:21:34.880 76d0 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.880 76d0 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 1092 : 0
2025/06/16-22:21:34.880 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.885 76d0 Generated table #29@1: 424 keys, 8048 bytes
2025/06/16-22:21:34.885 76d0 Compacted 1@1 + 1@2 files => 8048 bytes
2025/06/16-22:21:34.888 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.888 76d0 Delete type=2 #28
2025/06/16-22:21:34.889 76d0 Manual compaction at level-1 from '\x00\x0b\x00\x00\x05' @ 1092 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.889 76d0 Level-0 table #31: started
2025/06/16-22:21:34.905 76d0 Level-0 table #31: 249 bytes OK
2025/06/16-22:21:34.908 76d0 Delete type=2 #26
2025/06/16-22:21:34.908 76d0 Delete type=0 #27
2025/06/16-22:21:34.909 76d0 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.909 76d0 Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1098 : 0
2025/06/16-22:21:34.909 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.913 76d0 Generated table #32@1: 418 keys, 7956 bytes
2025/06/16-22:21:34.913 76d0 Compacted 1@1 + 1@2 files => 7956 bytes
2025/06/16-22:21:34.916 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.916 76d0 Delete type=2 #31
2025/06/16-22:21:34.916 76d0 Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1098 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.917 76d0 Level-0 table #34: started
2025/06/16-22:21:34.931 76d0 Level-0 table #34: 249 bytes OK
2025/06/16-22:21:34.934 76d0 Delete type=2 #29
2025/06/16-22:21:34.934 76d0 Delete type=0 #30
2025/06/16-22:21:34.935 76d0 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.935 76d0 Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1104 : 0
2025/06/16-22:21:34.935 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.941 76d0 Generated table #35@1: 412 keys, 7881 bytes
2025/06/16-22:21:34.941 76d0 Compacted 1@1 + 1@2 files => 7881 bytes
2025/06/16-22:21:34.944 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.944 76d0 Delete type=2 #34
2025/06/16-22:21:34.944 76d0 Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1104 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.945 76d0 Level-0 table #37: started
2025/06/16-22:21:34.960 76d0 Level-0 table #37: 1192 bytes OK
2025/06/16-22:21:34.963 76d0 Delete type=2 #32
2025/06/16-22:21:34.963 76d0 Delete type=0 #33
2025/06/16-22:21:34.963 76d0 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/16-22:21:34.963 76d0 Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1224 : 0
2025/06/16-22:21:34.963 76d0 Compacting 1@1 + 1@2 files
2025/06/16-22:21:34.968 76d0 Generated table #38@1: 292 keys, 6128 bytes
2025/06/16-22:21:34.968 76d0 Compacted 1@1 + 1@2 files => 6128 bytes
2025/06/16-22:21:34.970 76d0 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/16-22:21:34.971 76d0 Delete type=2 #37
2025/06/16-22:21:34.971 76d0 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1224 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'discord';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create database file if it doesn't exist
        $this->createDatabaseIfNotExists();

        // Discord Settings Table
        Schema::connection('discord')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('bot_token')->nullable();
            $table->string('client_id')->nullable();
            $table->string('client_secret')->nullable();
            $table->boolean('auto_reply')->default(false);
            $table->text('welcome_message')->nullable();
            $table->boolean('slash_commands')->default(true);
            $table->boolean('message_commands')->default(true);
            $table->boolean('dm_messages')->default(true);
            $table->json('permissions')->nullable(); // Bot permissions
            $table->timestamps();
        });

        // Discord Profiles Table (Users/Guilds/Channels)
        Schema::connection('discord')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('discord_id')->unique(); // Discord user/guild/channel ID
            $table->enum('type', ['user', 'guild', 'channel', 'role']);
            $table->string('name');
            $table->string('username')->nullable(); // For users
            $table->string('discriminator')->nullable(); // For users
            $table->string('avatar_url')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_bot')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->integer('members_count')->nullable(); // For guilds
            $table->string('parent_id')->nullable(); // For channels (guild ID)
            $table->json('metadata')->nullable(); // Additional info
            $table->timestamps();
        });

        // Discord Messages Table
        Schema::connection('discord')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Discord message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles (channel)
            $table->string('author_id'); // Discord user ID who sent the message
            $table->enum('type', ['text', 'embed', 'file', 'image', 'video', 'audio']);
            $table->text('content')->nullable(); // Message content
            $table->json('embeds')->nullable(); // Discord embeds
            $table->json('attachments')->nullable(); // File attachments
            $table->boolean('is_outgoing')->default(false); // true if sent by bot
            $table->boolean('is_edited')->default(false);
            $table->json('reactions')->nullable(); // Message reactions
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // Discord Commands Table
        Schema::connection('discord')->create('commands', function (Blueprint $table) {
            $table->id();
            $table->string('command_id')->unique(); // Discord command ID
            $table->string('name');
            $table->text('description');
            $table->enum('type', ['slash', 'message', 'user']);
            $table->json('options')->nullable(); // Command options
            $table->boolean('is_global')->default(true);
            $table->string('guild_id')->nullable(); // For guild-specific commands
            $table->boolean('enabled')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('discord')->dropIfExists('commands');
        Schema::connection('discord')->dropIfExists('messages');
        Schema::connection('discord')->dropIfExists('profiles');
        Schema::connection('discord')->dropIfExists('settings');
    }

    /**
     * Create database file if it doesn't exist
     */
    private function createDatabaseIfNotExists(): void
    {
        $databasePath = database_path('discord.sqlite');

        if (!file_exists($databasePath)) {
            // Create the database directory if it doesn't exist
            $databaseDir = dirname($databasePath);
            if (!is_dir($databaseDir)) {
                mkdir($databaseDir, 0755, true);
            }

            // Create empty SQLite file
            touch($databasePath);
        }
    }
};

<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatesDatabaseFile;

class WhatsAppSettings extends Model
{
    use HasFactory, CreatesDatabaseFile;

    protected $connection = 'whatsapp';
    protected $table = 'settings';

    protected $fillable = [
        'user_id',
        'enabled',
        'connected',
        'session_id',
        'qr_code',
        'client_info',
        'auto_reply',
        'welcome_message',
        'read_receipts',
        'typing_indicator',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'connected' => 'boolean',
        'client_info' => 'array',
        'auto_reply' => 'boolean',
        'read_receipts' => 'boolean',
        'typing_indicator' => 'boolean',
    ];

    /**
     * Get the settings for a specific user
     */
    public static function forUser($userId)
    {
        return static::where('user_id', $userId)->first();
    }
}

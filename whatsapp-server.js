import pkg from 'whatsapp-web.js';
const { Client, LocalAuth, MessageMedia } = pkg;
import express from 'express';
import cors from 'cors';
import qrcode from 'qrcode-terminal';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.WHATSAPP_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// WhatsApp client instance
let client = null;
let qrCodeData = null;
let isReady = false;
let clientInfo = null;

// Session storage directory
const sessionPath = './whatsapp-session';

// Initialize WhatsApp client
function initializeClient() {
    client = new Client({
        authStrategy: new LocalAuth({
            clientId: "whatsapp-web-session",
            dataPath: sessionPath
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu'
            ]
        }
    });

    // QR Code generation
    client.on('qr', (qr) => {
        console.log('QR Code received');
        qrCodeData = qr;

        // Generate QR code for terminal (optional)
        qrcode.generate(qr, { small: true });

        // Notify Laravel app about QR code
        notifyLaravelApp('qr_generated', { qr: qr });
    });

    // Client ready
    client.on('ready', async () => {
        console.log('WhatsApp Web is ready!');
        isReady = true;
        qrCodeData = null;

        // Get client info
        const info = client.info;
        clientInfo = {
            id: info.wid.user + '@' + info.wid.server,
            name: info.pushname,
            pushname: info.pushname,
            platform: info.platform,
            profilePicUrl: await client.getProfilePicUrl(info.wid._serialized).catch(() => null)
        };

        console.log('Client info:', clientInfo);

        // Notify Laravel app about successful connection
        notifyLaravelApp('connected', { clientInfo: clientInfo });
    });

    // Authentication success
    client.on('authenticated', () => {
        console.log('WhatsApp Web authenticated');
        notifyLaravelApp('authenticated', {});
    });

    // Authentication failure
    client.on('auth_failure', (msg) => {
        console.error('Authentication failed:', msg);
        notifyLaravelApp('auth_failure', { message: msg });
    });

    // Client disconnected
    client.on('disconnected', (reason) => {
        console.log('WhatsApp Web disconnected:', reason);
        isReady = false;
        clientInfo = null;
        qrCodeData = null;

        // Notify Laravel app about disconnection
        notifyLaravelApp('disconnected', { reason: reason });
    });

    // Message received
    client.on('message', async (message) => {
        console.log('Message received:', message.body);

        // Store message in database
        await storeMessage(message);

        // Notify Laravel app about new message
        notifyLaravelApp('message_received', {
            message: {
                id: message.id._serialized,
                body: message.body,
                from: message.from,
                to: message.to,
                timestamp: message.timestamp,
                type: message.type,
                isGroupMsg: message.isGroupMsg
            }
        });
    });

    // Initialize client
    client.initialize();
}

// Notify Laravel application
async function notifyLaravelApp(event, data) {
    try {
        // Use dynamic import for fetch in Node.js
        const fetch = (await import('node-fetch')).default;

        const response = await fetch('http://localhost:8000/api/whatsapp/webhook', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                event: event,
                data: data,
                timestamp: new Date().toISOString()
            })
        });

        if (!response.ok) {
            console.error('Failed to notify Laravel app:', response.statusText);
        } else {
            console.log(`✅ Notified Laravel app: ${event}`);
        }
    } catch (error) {
        console.error('Error notifying Laravel app:', error.message);
        // Don't fail if Laravel app is not running
    }
}

// Store message in database (will be handled by Laravel webhook)
async function storeMessage(message) {
    try {
        const contact = await message.getContact();
        const chat = await message.getChat();

        const messageData = {
            message_id: message.id._serialized,
            from: message.from,
            to: message.to,
            body: message.body,
            type: message.type,
            timestamp: message.timestamp,
            isGroupMsg: message.isGroupMsg,
            contact: {
                id: contact.id._serialized,
                name: contact.name,
                pushname: contact.pushname,
                isMyContact: contact.isMyContact,
                profilePicUrl: await contact.getProfilePicUrl().catch(() => null)
            },
            chat: {
                id: chat.id._serialized,
                name: chat.name,
                isGroup: chat.isGroup,
                unreadCount: chat.unreadCount
            }
        };

        // Send to Laravel for database storage
        await notifyLaravelApp('store_message', messageData);

    } catch (error) {
        console.error('Error processing message:', error);
    }
}

// API Routes

// Get current status
app.get('/api/status', (req, res) => {
    res.json({
        isReady: isReady,
        hasQR: !!qrCodeData,
        clientInfo: clientInfo,
        timestamp: new Date().toISOString()
    });
});

// Get QR code
app.get('/api/qr', (req, res) => {
    if (qrCodeData) {
        res.json({
            success: true,
            qr: qrCodeData,
            timestamp: new Date().toISOString()
        });
    } else {
        res.json({
            success: false,
            message: 'No QR code available',
            timestamp: new Date().toISOString()
        });
    }
});

// Generate new QR code
app.post('/api/generate-qr', (req, res) => {
    if (client && !isReady) {
        // Client will generate QR automatically
        res.json({
            success: true,
            message: 'QR code generation requested',
            timestamp: new Date().toISOString()
        });
    } else if (isReady) {
        res.json({
            success: false,
            message: 'Client is already connected',
            timestamp: new Date().toISOString()
        });
    } else {
        // Initialize client if not exists
        initializeClient();
        res.json({
            success: true,
            message: 'Client initialization started',
            timestamp: new Date().toISOString()
        });
    }
});

// Disconnect client
app.post('/api/disconnect', async (req, res) => {
    try {
        if (client) {
            await client.destroy();
            client = null;
            isReady = false;
            clientInfo = null;
            qrCodeData = null;

            // Clear session data
            if (fs.existsSync(sessionPath)) {
                fs.rmSync(sessionPath, { recursive: true, force: true });
            }
        }

        res.json({
            success: true,
            message: 'Client disconnected successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error disconnecting client: ' + error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Send message
app.post('/api/send-message', async (req, res) => {
    try {
        const { to, message } = req.body;

        if (!isReady) {
            return res.status(400).json({
                success: false,
                message: 'WhatsApp client is not ready',
                timestamp: new Date().toISOString()
            });
        }

        const result = await client.sendMessage(to, message);

        res.json({
            success: true,
            message: 'Message sent successfully',
            messageId: result.id._serialized,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error sending message: ' + error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        service: 'WhatsApp Web Server',
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`WhatsApp Web Server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);

    // Initialize WhatsApp client on startup
    initializeClient();
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down WhatsApp Web Server...');

    if (client) {
        await client.destroy();
    }

    process.exit(0);
});

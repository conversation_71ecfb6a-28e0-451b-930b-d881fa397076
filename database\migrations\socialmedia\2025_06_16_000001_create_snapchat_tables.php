<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'snapchat';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Snapchat Settings Table
        Schema::connection('snapchat')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('username')->nullable();
            $table->string('client_id')->nullable();
            $table->string('client_secret')->nullable();
            $table->string('access_token')->nullable();
            $table->string('refresh_token')->nullable();
            $table->boolean('auto_post')->default(false);
            $table->boolean('story_posting')->default(false);
            $table->boolean('spotlight_posting')->default(false);
            $table->integer('max_snaps_per_day')->default(10);
            $table->json('default_filters')->nullable(); // Default snap filters
            $table->timestamps();
        });

        // Snapchat Profiles Table (Friends/Contacts)
        Schema::connection('snapchat')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('snapchat_id')->unique(); // Snapchat user ID
            $table->string('username');
            $table->string('display_name')->nullable();
            $table->string('bitmoji_url')->nullable();
            $table->boolean('is_friend')->default(false);
            $table->boolean('is_blocked')->default(false);
            $table->integer('snap_score')->nullable();
            $table->enum('friendship_status', ['pending', 'friends', 'blocked', 'none'])->default('none');
            $table->json('metadata')->nullable(); // Additional profile info
            $table->timestamps();
        });

        // Snapchat Messages Table (Chats)
        Schema::connection('snapchat')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Snapchat message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'snap', 'video_snap', 'audio_snap', 'sticker', 'bitmoji']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For snap messages
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->boolean('is_opened')->default(false);
            $table->boolean('is_saved')->default(false);
            $table->integer('view_duration')->nullable(); // For snaps (seconds)
            $table->timestamp('expires_at')->nullable(); // When snap expires
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // Snapchat Stories Table
        Schema::connection('snapchat')->create('stories', function (Blueprint $table) {
            $table->id();
            $table->string('story_id')->unique(); // Snapchat story ID
            $table->enum('type', ['photo', 'video']);
            $table->string('media_url');
            $table->text('caption')->nullable();
            $table->json('filters')->nullable(); // Applied filters
            $table->integer('views_count')->default(0);
            $table->boolean('is_public')->default(true);
            $table->timestamp('expires_at'); // 24 hours from posting
            $table->timestamp('posted_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('snapchat')->dropIfExists('stories');
        Schema::connection('snapchat')->dropIfExists('messages');
        Schema::connection('snapchat')->dropIfExists('profiles');
        Schema::connection('snapchat')->dropIfExists('settings');
    }
};

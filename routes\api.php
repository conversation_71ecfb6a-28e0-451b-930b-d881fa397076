<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WhatsAppController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WhatsApp API Routes
Route::prefix('whatsapp')->group(function () {
    Route::post('/webhook', [WhatsAppController::class, 'webhook']);
    Route::get('/status', [WhatsAppController::class, 'getStatus']);
    Route::post('/generate-qr', [WhatsAppController::class, 'generateQR']);
    Route::post('/disconnect', [WhatsAppController::class, 'disconnect']);
    Route::post('/send-message', [WhatsAppController::class, 'sendMessage']);
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'telegram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Telegram Settings Table
        Schema::connection('telegram')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('bot_token')->nullable();
            $table->string('bot_username')->nullable();
            $table->string('webhook_url')->nullable();
            $table->boolean('auto_reply')->default(false);
            $table->text('welcome_message')->nullable();
            $table->boolean('group_messages')->default(true);
            $table->boolean('private_messages')->default(true);
            $table->boolean('channel_posts')->default(false);
            $table->json('commands')->nullable(); // Bot commands
            $table->timestamps();
        });

        // Telegram Profiles Table (Users/Chats)
        Schema::connection('telegram')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('telegram_id')->unique(); // Telegram user/chat ID
            $table->enum('type', ['user', 'group', 'supergroup', 'channel']);
            $table->string('username')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('title')->nullable(); // For groups/channels
            $table->text('description')->nullable();
            $table->string('photo_url')->nullable();
            $table->boolean('is_bot')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->integer('members_count')->nullable(); // For groups/channels
            $table->json('metadata')->nullable(); // Additional info
            $table->timestamps();
        });

        // Telegram Messages Table
        Schema::connection('telegram')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id'); // Telegram message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'photo', 'video', 'audio', 'document', 'sticker', 'location', 'contact', 'voice']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For media messages
            $table->boolean('is_outgoing')->default(false); // true if sent by bot
            $table->boolean('is_edited')->default(false);
            $table->boolean('is_forwarded')->default(false);
            $table->json('reply_to')->nullable(); // Reply information
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // Telegram Updates Table (Webhook updates)
        Schema::connection('telegram')->create('updates', function (Blueprint $table) {
            $table->id();
            $table->string('update_id')->unique(); // Telegram update ID
            $table->enum('type', ['message', 'edited_message', 'callback_query', 'inline_query']);
            $table->json('data'); // Full update data
            $table->boolean('processed')->default(false);
            $table->timestamp('received_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('telegram')->dropIfExists('updates');
        Schema::connection('telegram')->dropIfExists('messages');
        Schema::connection('telegram')->dropIfExists('profiles');
        Schema::connection('telegram')->dropIfExists('settings');
    }
};

<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'messages';

    protected $fillable = [
        'message_id',
        'profile_id',
        'type',
        'content',
        'media_url',
        'is_outgoing',
        'is_read',
        'sent_at',
    ];

    protected $casts = [
        'is_outgoing' => 'boolean',
        'is_read' => 'boolean',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the profile that owns this message
     */
    public function profile(): BelongsTo
    {
        return $this->belongsTo(WhatsAppProfile::class, 'profile_id');
    }

    /**
     * Scope for incoming messages
     */
    public function scopeIncoming($query)
    {
        return $query->where('is_outgoing', false);
    }

    /**
     * Scope for outgoing messages
     */
    public function scopeOutgoing($query)
    {
        return $query->where('is_outgoing', true);
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }
}

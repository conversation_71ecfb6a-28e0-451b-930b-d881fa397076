<?php

namespace App\Models\Instagram;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InstagramProfile extends Model
{
    use HasFactory;

    protected $connection = 'instagram';
    protected $table = 'profiles';

    protected $fillable = [
        'instagram_id',
        'username',
        'full_name',
        'profile_picture',
        'bio',
        'followers_count',
        'following_count',
        'posts_count',
        'is_verified',
        'is_business',
        'is_following',
        'follows_me',
        'metadata',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'is_business' => 'boolean',
        'is_following' => 'boolean',
        'follows_me' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get all messages for this profile
     */
    public function messages(): HasMany
    {
        return $this->hasMany(InstagramMessage::class, 'profile_id');
    }

    /**
     * Get all posts for this profile
     */
    public function posts(): Has<PERSON>any
    {
        return $this->hasMany(InstagramPost::class, 'profile_id');
    }
}

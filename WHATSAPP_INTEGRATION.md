# WhatsApp Web Integration

This document explains how to set up and use the WhatsApp Web integration with whatsapp-web.js.

## Overview

The WhatsApp integration consists of:
- **Node.js Server** (`whatsapp-server.js`) - Handles WhatsApp Web connection
- **Laravel API** (`WhatsAppController`) - Manages data and communication
- **Livewire Component** (`WhatsappSettings`) - User interface
- **SQLite Database** (`whatsapp.sqlite`) - Stores messages and settings

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp Web  │◄──►│  Node.js Server │◄──►│  Laravel App    │
│   (Browser)     │    │ (whatsapp-web.js)│    │  (API/UI)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │ WhatsApp SQLite │
                                               │   Database      │
                                               └─────────────────┘
```

## Setup Instructions

### 1. Install Dependencies

The npm packages should already be installed. If not:

```bash
npm install whatsapp-web.js qrcode-terminal express cors
```

### 2. Start the Services

#### Option A: Using the batch file (Windows)
```bash
start-whatsapp.bat
```

#### Option B: Using npm scripts
```bash
# Start WhatsApp server only
npm run whatsapp

# Start both Vite and WhatsApp server
npm run whatsapp:dev
```

#### Option C: Manual startup
```bash
# Terminal 1: Start Laravel
php artisan serve

# Terminal 2: Start WhatsApp server
node whatsapp-server.js
```

### 3. Configure WhatsApp

1. Go to **Settings → Social Media → WhatsApp**
2. Click **"Generate QR Code"**
3. Scan the QR code with your WhatsApp mobile app:
   - Open WhatsApp on your phone
   - Go to **Settings → Linked Devices**
   - Tap **"Link a Device"**
   - Scan the QR code displayed in the browser

## API Endpoints

### WhatsApp Server (Node.js - Port 3001)

- `GET /api/status` - Get connection status
- `GET /api/qr` - Get current QR code
- `POST /api/generate-qr` - Generate new QR code
- `POST /api/disconnect` - Disconnect WhatsApp
- `POST /api/send-message` - Send a message
- `GET /health` - Health check

### Laravel API (Port 8000)

- `POST /api/whatsapp/webhook` - Webhook for WhatsApp events
- `GET /api/whatsapp/status` - Proxy to Node.js status
- `POST /api/whatsapp/generate-qr` - Proxy to generate QR
- `POST /api/whatsapp/disconnect` - Proxy to disconnect
- `POST /api/whatsapp/send-message` - Proxy to send message

## Database Schema

### WhatsApp Database (`database/whatsapp.sqlite`)

#### Settings Table
```sql
- id (Primary Key)
- user_id (Reference to main app user)
- enabled (Boolean)
- connected (Boolean)
- session_id (WhatsApp session ID)
- qr_code (Current QR code)
- client_info (JSON - Connected user info)
- auto_reply (Boolean)
- welcome_message (Text)
- read_receipts (Boolean)
- typing_indicator (Boolean)
- timestamps
```

#### Profiles Table
```sql
- id (Primary Key)
- whatsapp_id (Unique WhatsApp contact ID)
- phone_number (Phone number)
- name (Contact name)
- profile_picture (Profile picture URL)
- is_business (Boolean)
- is_group (Boolean)
- metadata (JSON - Additional contact info)
- last_seen (Timestamp)
- timestamps
```

#### Messages Table
```sql
- id (Primary Key)
- message_id (Unique WhatsApp message ID)
- profile_id (Foreign key to profiles)
- type (text, image, video, audio, document, location, contact)
- content (Message content)
- media_url (For media messages)
- is_outgoing (Boolean - sent by us)
- is_read (Boolean)
- sent_at (Timestamp)
- timestamps
```

#### Conversations Table
```sql
- id (Primary Key)
- profile_id (Foreign key to profiles)
- last_message_id (Foreign key to messages)
- unread_count (Integer)
- is_archived (Boolean)
- is_pinned (Boolean)
- last_activity (Timestamp)
- timestamps
```

## Features

### ✅ Implemented
- QR code generation and scanning
- Real-time connection status
- Automatic database creation
- Message storage
- Contact/profile management
- Connection management (connect/disconnect)
- Real-time polling for status updates
- Webhook integration
- Session persistence

### 🚧 Planned
- Message sending interface
- Auto-reply functionality
- Message templates
- Group message handling
- Media message support
- Message search and filtering
- Conversation management UI
- Bulk messaging
- Message scheduling

## Usage Examples

### Sending a Message (API)
```bash
curl -X POST http://localhost:3001/api/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "message": "Hello from WhatsApp Web!"
  }'
```

### Checking Status
```bash
curl http://localhost:3001/api/status
```

## Troubleshooting

### Common Issues

1. **"WhatsApp server is not running"**
   - Make sure Node.js server is started: `node whatsapp-server.js`
   - Check if port 3001 is available

2. **QR Code not generating**
   - Restart the WhatsApp server
   - Clear browser cache
   - Check Node.js console for errors

3. **Connection lost**
   - WhatsApp Web sessions can expire
   - Generate a new QR code to reconnect
   - Check your phone's internet connection

4. **Database errors**
   - Run migrations: `php artisan social:migrate`
   - Check database file permissions

### Logs

- **Node.js logs**: Check the terminal running `whatsapp-server.js`
- **Laravel logs**: Check `storage/logs/laravel.log`
- **Browser console**: Check for JavaScript errors

## Security Notes

- The WhatsApp session is stored locally in `./whatsapp-session/`
- QR codes are temporary and expire after a few minutes
- API endpoints should be secured in production
- Consider rate limiting for message sending

## Production Deployment

1. Use PM2 or similar for Node.js process management
2. Set up proper environment variables
3. Configure reverse proxy (nginx/Apache)
4. Enable HTTPS for all endpoints
5. Set up monitoring and logging
6. Configure firewall rules

## Environment Variables

Add to your `.env` file:

```env
WHATSAPP_SERVER_URL=http://localhost:3001
```

For production:
```env
WHATSAPP_SERVER_URL=https://your-domain.com:3001
```

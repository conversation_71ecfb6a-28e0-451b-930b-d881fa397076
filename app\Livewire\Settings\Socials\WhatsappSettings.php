<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use App\Models\WhatsApp\WhatsAppSettings as WhatsAppSettingsModel;

class WhatsappSettings extends Component
{
    public $isConnected = false;
    public $qrCode = '';
    public $sessionStatus = 'disconnected';
    public $userInfo = [];
    public $connectionTime = null;

    protected $settings;

    public function mount()
    {
        $this->loadSettings();
        $this->checkStatus();
    }

    private function loadSettings()
    {
        $user = Auth::user();
        $userId = $user ? $user->id : 1;

        $this->settings = WhatsAppSettingsModel::forUser($userId);
        if (!$this->settings) {
            $this->settings = WhatsAppSettingsModel::create([
                'user_id' => $userId,
                'connected' => false,
            ]);
        }
    }

    public function checkStatus()
    {
        try {
            $response = Http::timeout(5)->get('http://localhost:3001/api/status');

            if ($response->successful()) {
                $data = $response->json();

                if ($data['isReady'] && $data['clientInfo']) {
                    $this->setConnected($data['clientInfo']);
                } elseif ($data['hasQR']) {
                    $this->getQrCode();
                } else {
                    $this->generateQrCode();
                }
            } else {
                $this->setDisconnected();
            }
        } catch (\Exception $e) {
            $this->setDisconnected();
        }
    }

    private function setConnected($clientInfo)
    {
        $this->isConnected = true;
        $this->sessionStatus = 'connected';
        $this->qrCode = '';
        $this->userInfo = $clientInfo;
        $this->connectionTime = now();

        $this->settings->update([
            'connected' => true,
            'client_info' => $clientInfo,
            'session_id' => $clientInfo['id'] ?? null
        ]);
    }

    private function setDisconnected()
    {
        $this->isConnected = false;
        $this->sessionStatus = 'disconnected';
        $this->qrCode = '';
        $this->userInfo = [];
        $this->connectionTime = null;
    }

    public function generateQrCode()
    {
        try {
            $response = Http::timeout(10)->post('http://localhost:3001/api/generate-qr');

            if ($response->successful()) {
                $this->sessionStatus = 'connecting';
                $this->dispatch('start-polling');
                $this->getQrCode();
            }
        } catch (\Exception $e) {
            // Handle silently
        }
    }

    private function getQrCode()
    {
        try {
            $qrResponse = Http::timeout(10)->get('http://localhost:3001/api/qr');

            if ($qrResponse->successful()) {
                $qrData = $qrResponse->json();

                if ($qrData['success']) {
                    $qrImageResponse = Http::timeout(10)->get('https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=' . urlencode($qrData['qr']));

                    if ($qrImageResponse->successful()) {
                        $this->qrCode = 'data:image/png;base64,' . base64_encode($qrImageResponse->body());
                        $this->sessionStatus = 'connecting';
                        $this->dispatch('start-polling');
                        $this->dispatch('start-qr-timer');
                    }
                }
            }
        } catch (\Exception $e) {
            // Handle silently
        }
    }

    public function refreshQrCode()
    {
        $this->generateQrCode();

        $this->dispatch('toastify', [
            'type' => 'success',
            'message' => 'QR Code refreshed!'
        ]);
    }

    public function forceRefreshQr()
    {
        $this->refreshQrCode();
    }

    public function qrExpired()
    {
        $this->dispatch('toastify', [
            'type' => 'info',
            'message' => 'QR Code expired. Generating new code...'
        ]);

        $this->generateQrCode();
    }

    public function refreshStatus()
    {
        $this->checkStatus();

        $this->dispatch('toastify', [
            'type' => 'info',
            'message' => 'Status refreshed'
        ]);
    }

    public function simulateConnection()
    {
        $this->setConnected([
            'id' => '+<EMAIL>',
            'name' => 'Test User',
            'pushname' => 'Test User',
            'profilePicUrl' => 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=TU',
            'platform' => 'android',
        ]);

        $this->dispatch('toastify', [
            'type' => 'success',
            'message' => 'WhatsApp Web connected successfully!'
        ]);
    }

    public function checkConnectionStatus()
    {
        try {
            $response = Http::timeout(10)->get(url('/api/whatsapp/status'));

            if ($response->successful()) {
                $data = $response->json();

                if ($data['isReady']) {
                    $this->setConnected($data['clientInfo'] ?? []);
                    $this->dispatch('toastify', [
                        'type' => 'success',
                        'message' => 'WhatsApp Web connected successfully!'
                    ]);
                    $this->dispatch('stop-polling');
                } elseif ($data['hasQR']) {
                    $this->getQrCode();
                }
            }
        } catch (\Exception $e) {
            // Handle silently during polling
        }
    }

    public function disconnect()
    {
        try {
            $this->setDisconnected();

            $this->settings->update([
                'connected' => false,
                'qr_code' => null,
                'client_info' => null,
                'session_id' => null,
            ]);

            $this->dispatch('stop-polling');

            $response = Http::timeout(10)->post(url('/api/whatsapp/disconnect'));

            if ($response->successful()) {
                $this->dispatch('toastify', [
                    'type' => 'success',
                    'message' => 'WhatsApp disconnected successfully!'
                ]);
            } else {
                $this->dispatch('toastify', [
                    'type' => 'warning',
                    'message' => 'WhatsApp disconnected locally.'
                ]);
            }
        } catch (\Exception $e) {
            $this->dispatch('toastify', [
                'type' => 'warning',
                'message' => 'WhatsApp disconnected locally.'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}

# Social Media Databases

This document explains the separate SQLite database structure for each social media platform.

## Overview

Each social media platform has its own SQLite database to store:
- Platform-specific settings
- User profiles/contacts from the platform
- Messages and conversations
- Platform-specific data (posts, stories, etc.)

## Database Structure

### Platforms
- **WhatsApp** (`database/whatsapp.sqlite`)
- **Instagram** (`database/instagram.sqlite`)
- **Telegram** (`database/telegram.sqlite`)
- **Discord** (`database/discord.sqlite`)
- **Facebook** (`database/facebook.sqlite`)
- **Snapchat** (`database/snapchat.sqlite`)

### Common Tables Structure

Each platform database contains:

#### Settings Table
- `user_id` - Reference to main app user
- `enabled` - Platform enabled/disabled
- `connected` - Connection status
- Platform-specific configuration fields

#### Profiles Table
- Platform user/contact information
- Profile pictures, names, metadata
- Connection status (friends, followers, etc.)

#### Messages Table
- All messages/conversations
- Message types (text, media, etc.)
- Read status, timestamps
- Media URLs

#### Platform-Specific Tables
- **Instagram**: Posts table for tracking posts
- **Discord**: Commands table for bot commands
- **Telegram**: Updates table for webhook data
- **Snapchat**: Stories table for story management
- **Facebook**: Posts table for page posts

## Usage

### Running Migrations

```bash
# Run all social media database migrations
php artisan social:migrate

# Fresh migration (drops all tables first)
php artisan social:migrate --fresh
```

**Note**: The migration command automatically creates SQLite database files if they don't exist. Each migration also includes a `createDatabaseIfNotExists()` method that ensures the database file is created before running the migration.

### Using Models

```php
// WhatsApp
use App\Models\WhatsApp\WhatsAppSettings;
use App\Models\WhatsApp\WhatsAppProfile;
use App\Models\WhatsApp\WhatsAppMessage;

// Get settings for current user
$settings = WhatsAppSettings::forUser(auth()->user()->id);

// Get all profiles
$profiles = WhatsAppProfile::all();

// Get messages for a profile
$messages = WhatsAppMessage::where('profile_id', $profileId)->get();
```

### Database Connections

Each platform uses its own database connection defined in `config/database.php`:

```php
'whatsapp' => [
    'driver' => 'sqlite',
    'database' => database_path('whatsapp.sqlite'),
    'prefix' => '',
    'foreign_key_constraints' => true,
],
```

## Automatic Database Creation

The system automatically creates SQLite database files when needed:

1. **Migration Command**: The `php artisan social:migrate` command checks for and creates missing database files
2. **Individual Migrations**: Each migration file includes a `createDatabaseIfNotExists()` method
3. **Model Trait**: The `CreatesDatabaseFile` trait can be used in models to ensure database files exist
4. **Directory Creation**: The system automatically creates the `database/` directory if it doesn't exist

### Database File Locations

All SQLite files are created in the `database/` directory:
- `database/whatsapp.sqlite`
- `database/instagram.sqlite`
- `database/telegram.sqlite`
- `database/discord.sqlite`
- `database/facebook.sqlite`
- `database/snapchat.sqlite`

## Benefits

1. **Scalability**: Each platform can handle large amounts of data independently
2. **Performance**: Queries are faster with smaller, focused databases
3. **Maintenance**: Easy to backup, restore, or migrate individual platforms
4. **Isolation**: Issues with one platform don't affect others
5. **Flexibility**: Each platform can have its own schema optimizations
6. **Auto-Creation**: Database files are created automatically when needed

## File Structure

```
database/
├── migrations/
│   ├── whatsapp/
│   │   └── 2025_06_16_000001_create_whatsapp_tables.php
│   ├── instagram/
│   │   └── 2025_06_16_000001_create_instagram_tables.php
│   ├── telegram/
│   │   └── 2025_06_16_000001_create_telegram_tables.php
│   ├── discord/
│   │   └── 2025_06_16_000001_create_discord_tables.php
│   ├── facebook/
│   │   └── 2025_06_16_000001_create_facebook_tables.php
│   └── snapchat/
│       └── 2025_06_16_000001_create_snapchat_tables.php
├── whatsapp.sqlite
├── instagram.sqlite
├── telegram.sqlite
├── discord.sqlite
├── facebook.sqlite
└── snapchat.sqlite

app/Models/
├── WhatsApp/
│   ├── WhatsAppSettings.php
│   ├── WhatsAppProfile.php
│   ├── WhatsAppMessage.php
│   └── WhatsAppConversation.php
├── Instagram/
│   ├── InstagramSettings.php
│   ├── InstagramProfile.php
│   ├── InstagramMessage.php
│   └── InstagramPost.php
└── [Other platforms...]
```

## Next Steps

1. Run the migrations: `php artisan social:migrate`
2. Update your Livewire components to use the new models
3. Test the database connections
4. Implement platform-specific features using the new structure

## Notes

- All models use the appropriate database connection automatically
- Foreign key constraints are enabled for data integrity
- Each platform's settings are isolated from the main user table
- The structure is designed to be simple and efficient for high-volume messaging

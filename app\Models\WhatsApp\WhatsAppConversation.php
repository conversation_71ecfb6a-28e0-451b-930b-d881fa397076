<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppConversation extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'conversations';

    protected $fillable = [
        'profile_id',
        'last_message_id',
        'unread_count',
        'is_archived',
        'is_pinned',
        'last_activity',
    ];

    protected $casts = [
        'is_archived' => 'boolean',
        'is_pinned' => 'boolean',
        'last_activity' => 'datetime',
    ];

    /**
     * Get the profile for this conversation
     */
    public function profile(): BelongsTo
    {
        return $this->belongsTo(WhatsAppProfile::class, 'profile_id');
    }

    /**
     * Get the last message
     */
    public function lastMessage(): BelongsTo
    {
        return $this->belongsTo(WhatsAppMessage::class, 'last_message_id');
    }

    /**
     * Mark conversation as read
     */
    public function markAsRead()
    {
        $this->update(['unread_count' => 0]);
    }

    /**
     * Increment unread count
     */
    public function incrementUnread()
    {
        $this->increment('unread_count');
    }
}

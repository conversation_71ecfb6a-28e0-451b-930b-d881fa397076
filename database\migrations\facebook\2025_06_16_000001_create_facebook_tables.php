<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'facebook';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Facebook Settings Table
        Schema::connection('facebook')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('page_id')->nullable();
            $table->string('access_token')->nullable();
            $table->string('app_id')->nullable();
            $table->string('app_secret')->nullable();
            $table->boolean('auto_post')->default(false);
            $table->boolean('auto_reply')->default(false);
            $table->boolean('auto_like')->default(false);
            $table->boolean('auto_comment')->default(false);
            $table->boolean('messenger_enabled')->default(true);
            $table->boolean('page_insights')->default(true);
            $table->integer('max_posts_per_day')->default(5);
            $table->text('welcome_message')->nullable();
            $table->timestamps();
        });

        // Facebook Profiles Table (Page followers, message senders)
        Schema::connection('facebook')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('facebook_id')->unique(); // Facebook user ID
            $table->string('name');
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('profile_picture')->nullable();
            $table->string('locale')->nullable();
            $table->string('timezone')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->boolean('is_page_follower')->default(false);
            $table->json('metadata')->nullable(); // Additional profile info
            $table->timestamps();
        });

        // Facebook Messages Table (Messenger)
        Schema::connection('facebook')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Facebook message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'image', 'video', 'audio', 'file', 'template', 'quick_reply']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For media messages
            $table->boolean('is_outgoing')->default(false); // true if sent by page
            $table->boolean('is_read')->default(false);
            $table->json('quick_replies')->nullable(); // Quick reply options
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // Facebook Posts Table
        Schema::connection('facebook')->create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('post_id')->unique(); // Facebook post ID
            $table->enum('type', ['status', 'photo', 'video', 'link', 'event']);
            $table->text('message')->nullable(); // Post content
            $table->string('media_url')->nullable(); // For media posts
            $table->string('link_url')->nullable(); // For link posts
            $table->integer('likes_count')->default(0);
            $table->integer('comments_count')->default(0);
            $table->integer('shares_count')->default(0);
            $table->integer('reactions_count')->default(0);
            $table->json('reactions_breakdown')->nullable(); // Like, Love, Haha, etc.
            $table->boolean('is_published')->default(true);
            $table->timestamp('posted_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('facebook')->dropIfExists('posts');
        Schema::connection('facebook')->dropIfExists('messages');
        Schema::connection('facebook')->dropIfExists('profiles');
        Schema::connection('facebook')->dropIfExists('settings');
    }
};

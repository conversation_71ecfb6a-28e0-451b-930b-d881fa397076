<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Console\Events\CommandFinished;
use Illuminate\Support\Facades\Event;

class SocialMediaMigrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Listen for migration commands and optionally run social migrations
        Event::listen(CommandFinished::class, function (CommandFinished $event) {
            // Check if this is a migration command and if --with-social flag is present
            if ($this->shouldRunSocialMigrations($event)) {
                $this->runSocialMigrations($event);
            }
        });
    }

    /**
     * Determine if social migrations should be run
     */
    private function shouldRunSocialMigrations(CommandFinished $event): bool
    {
        $command = $event->command;
        $input = $event->input;

        // Check if this is a migration command
        $migrationCommands = ['migrate', 'migrate:fresh', 'migrate:refresh', 'migrate:reset'];
        
        if (!in_array($command, $migrationCommands)) {
            return false;
        }

        // Check if --with-social flag is present
        return $input->hasOption('with-social') && $input->getOption('with-social');
    }

    /**
     * Run social media migrations
     */
    private function runSocialMigrations(CommandFinished $event): void
    {
        $command = $event->command;
        
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Running Social Media Database Migrations...\n";
        echo str_repeat('=', 50) . "\n";

        try {
            // Determine which social migration command to run
            $socialCommand = 'social:migrate';
            $socialOptions = [];

            if (in_array($command, ['migrate:fresh', 'migrate:refresh', 'migrate:reset'])) {
                $socialOptions['--fresh'] = true;
            }

            // Run the social migration command
            Artisan::call($socialCommand, $socialOptions);
            echo Artisan::output();

        } catch (\Exception $e) {
            echo "Error running social media migrations: " . $e->getMessage() . "\n";
        }
    }
}

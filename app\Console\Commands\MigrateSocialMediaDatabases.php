<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class MigrateSocialMediaDatabases extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'social:migrate {--fresh : Drop all tables and re-run all migrations}';

    /**
     * The console command description.
     */
    protected $description = 'Run migrations for all social media platform databases';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $platforms = ['whatsapp', 'instagram', 'telegram', 'discord', 'facebook', 'snapchat'];

        $this->info('Starting social media database migrations...');

        foreach ($platforms as $platform) {
            $this->info("Migrating {$platform} database...");

            try {
                // Create database file if it doesn't exist
                $this->createDatabaseIfNotExists($platform);

                // Test database connection
                DB::connection($platform)->getPdo();

                // Run migrations for this platform
                if ($this->option('fresh')) {
                    Artisan::call('migrate:fresh', [
                        '--database' => $platform,
                        '--path' => "database/migrations/{$platform}",
                        '--force' => true,
                    ]);
                } else {
                    Artisan::call('migrate', [
                        '--database' => $platform,
                        '--path' => "database/migrations/{$platform}",
                        '--force' => true,
                    ]);
                }

                $this->info("✅ {$platform} database migrated successfully");

            } catch (\Exception $e) {
                $this->error("❌ Failed to migrate {$platform} database: " . $e->getMessage());
            }
        }

        $this->info('Social media database migrations completed!');
    }

    /**
     * Create database file if it doesn't exist
     */
    private function createDatabaseIfNotExists($platform)
    {
        $databasePath = database_path("{$platform}.sqlite");

        if (!file_exists($databasePath)) {
            $this->info("Creating {$platform}.sqlite database file...");

            // Create the database directory if it doesn't exist
            $databaseDir = dirname($databasePath);
            if (!is_dir($databaseDir)) {
                mkdir($databaseDir, 0755, true);
            }

            // Create empty SQLite file
            touch($databasePath);

            $this->info("✅ Created {$platform}.sqlite database file");
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // WhatsApp Settings Table
        Schema::connection('whatsapp')->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('session_id')->nullable();
            $table->json('client_info')->nullable(); // Store client info from whatsapp-web.js
            $table->boolean('auto_reply')->default(false);
            $table->text('welcome_message')->nullable();
            $table->boolean('read_receipts')->default(true);
            $table->boolean('typing_indicator')->default(true);
            $table->timestamps();
        });

        // WhatsApp Profiles Table (Contacts)
        Schema::connection('whatsapp')->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('whatsapp_id')->unique(); // WhatsApp contact ID
            $table->string('phone_number');
            $table->string('name')->nullable();
            $table->string('profile_picture')->nullable();
            $table->boolean('is_business')->default(false);
            $table->boolean('is_group')->default(false);
            $table->json('metadata')->nullable(); // Additional contact info
            $table->timestamp('last_seen')->nullable();
            $table->timestamps();
        });

        // WhatsApp Messages Table
        Schema::connection('whatsapp')->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // WhatsApp message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'image', 'video', 'audio', 'document', 'location', 'contact']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For media messages
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->boolean('is_read')->default(false);
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });

        // WhatsApp Conversations Table
        Schema::connection('whatsapp')->create('conversations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->unsignedBigInteger('last_message_id')->nullable();
            $table->integer('unread_count')->default(0);
            $table->boolean('is_archived')->default(false);
            $table->boolean('is_pinned')->default(false);
            $table->timestamp('last_activity')->nullable();
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
            $table->foreign('last_message_id')->references('id')->on('messages')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('whatsapp')->dropIfExists('conversations');
        Schema::connection('whatsapp')->dropIfExists('messages');
        Schema::connection('whatsapp')->dropIfExists('profiles');
        Schema::connection('whatsapp')->dropIfExists('settings');
    }
};
